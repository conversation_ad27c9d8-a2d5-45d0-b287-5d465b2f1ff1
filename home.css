/* Home Page Specific Styles for ImgNinja Landing Page */

/* Home Container */
.home-container {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Hero Landing Section */
.hero-landing {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 100px 20px 60px;
  overflow: hidden;
  background: var(--primary-color);
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg,
    var(--primary-color) 0%,
    var(--secondary-color) 20%,
    var(--accent-color) 40%,
    var(--secondary-color) 60%,
    var(--primary-color) 80%,
    var(--secondary-color) 100%);
  opacity: 0.9;
  z-index: -3;
}

.hero-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.06) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.04) 0%, transparent 70%),
    linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.02) 50%, transparent 70%);
  z-index: -2;
}

.hero-background::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    repeating-linear-gradient(
      90deg,
      transparent,
      transparent 2px,
      rgba(255, 255, 255, 0.01) 2px,
      rgba(255, 255, 255, 0.01) 4px
    ),
    repeating-linear-gradient(
      0deg,
      transparent,
      transparent 2px,
      rgba(255, 255, 255, 0.01) 2px,
      rgba(255, 255, 255, 0.01) 4px
    );
  z-index: -1;
}

.hero-content {
  text-align: center;
  max-width: 900px;
  z-index: 10;
  position: relative;
}

.hero-visual {
  margin-bottom: 40px;
  animation: fadeInUp 1.2s ease-out;
}

.modern-logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.logo-circle {
  position: relative;
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: logoFloat 4s ease-in-out infinite;
}

.logo-inner {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--highlight-color), rgba(255, 255, 255, 0.8));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 10px 30px rgba(255, 255, 255, 0.3),
    inset 0 2px 10px rgba(255, 255, 255, 0.2),
    0 0 20px rgba(255, 255, 255, 0.2);
  z-index: 3;
  position: relative;
  overflow: hidden;
}

.favicon-logo {
  width: 50px;
  height: 50px;
  object-fit: contain;
  filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.3));
  transition: transform 0.3s ease;
}

.logo-inner:hover .favicon-logo {
  transform: scale(1.1);
}

.logo-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: logoRotate 8s linear infinite;
  border-top-color: var(--highlight-color);
  border-right-color: transparent;
}

.logo-ring-2 {
  position: absolute;
  width: 140px;
  height: 140px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  animation: logoRotate 12s linear infinite reverse;
  border-bottom-color: var(--highlight-color);
  border-left-color: transparent;
  top: -10px;
  left: -10px;
}

.logo-ring-3 {
  position: absolute;
  width: 160px;
  height: 160px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  animation: logoRotate 20s linear infinite;
  border-top-color: rgba(255, 255, 255, 0.4);
  border-right-color: transparent;
  border-bottom-color: transparent;
  border-left-color: rgba(255, 255, 255, 0.2);
  top: -20px;
  left: -20px;
}


.hero-title {
  font-size: 4rem;
  font-weight: 800;
  margin-bottom: 25px;
  color: var(--highlight-color);
  animation: fadeInUp 1.2s ease-out 0.3s both;
  line-height: 1.2;
  letter-spacing: -0.02em;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: 0.3em;
}

.welcome-text {
  color: var(--highlight-color);
  display: inline-block;
}

.brand-highlight {
  background: linear-gradient(135deg,
    #ffffff 0%,
    #f8f9fa 8%,
    #e9ecef 16%,
    #dee2e6 24%,
    #ced4da 32%,
    #adb5bd 40%,
    #6c757d 48%,
    #495057 56%,
    #343a40 64%,
    #212529 72%,
    #495057 80%,
    #6c757d 88%,
    #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 400% 400%;
  animation: advancedGradientFlow 6s ease-in-out infinite;
  position: relative;
  display: inline-block;
  font-weight: 900;
  text-shadow: none;
  filter:
    drop-shadow(0 0 20px rgba(255, 255, 255, 0.4))
    drop-shadow(0 0 40px rgba(255, 255, 255, 0.2))
    drop-shadow(0 2px 8px rgba(0, 0, 0, 0.3));
  transform-style: preserve-3d;
  perspective: 1000px;
}

.brand-highlight::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg,
    transparent 0%,
    rgba(255, 255, 255, 0.1) 25%,
    rgba(255, 255, 255, 0.6) 50%,
    rgba(255, 255, 255, 0.1) 75%,
    transparent 100%);
  background-size: 300% 300%;
  animation: modernShimmer 3s ease-in-out infinite;
  z-index: 1;
  pointer-events: none;
  border-radius: 8px;
  filter: blur(1px);
}

.brand-highlight::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.05) 20%,
    rgba(255, 255, 255, 0.15) 40%,
    rgba(255, 255, 255, 0.25) 50%,
    rgba(255, 255, 255, 0.15) 60%,
    rgba(255, 255, 255, 0.05) 80%,
    transparent 100%);
  background-size: 200% 100%;
  animation: lightSweep 4s ease-in-out infinite;
  z-index: 2;
  pointer-events: none;
  mix-blend-mode: overlay;
}

.brand-highlight:hover {
  animation-duration: 2s;
  filter:
    drop-shadow(0 0 30px rgba(255, 255, 255, 0.6))
    drop-shadow(0 0 60px rgba(255, 255, 255, 0.4))
    drop-shadow(0 4px 12px rgba(0, 0, 0, 0.4));
  transform: scale(1.05) translateZ(10px);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.brand-highlight:hover::before {
  animation-duration: 1.5s;
  filter: blur(0.5px);
}

.brand-highlight:hover::after {
  animation-duration: 2s;
  mix-blend-mode: screen;
}



.hero-subtitle {
  font-size: 1.8rem;
  font-weight: 600;
  color: #e0e0e069;
  margin-bottom: 25px;
  animation: fadeInUp 1.2s ease-out 0.6s both;
  opacity: 0.9;
  letter-spacing: 0.5px;
}

.hero-description {
  font-size: 1.2rem;
  color: var(--text-color);
  line-height: 1.7;
  margin-bottom: 50px;
  animation: fadeInUp 1.2s ease-out 0.9s both;
  opacity: 0.85;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.hero-cta {
  display: flex;
  gap: 25px;
  justify-content: center;
  flex-wrap: wrap;
  animation: fadeInUp 1.2s ease-out 1.2s both;
}

.cta-button {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  padding: 18px 35px;
  border-radius: 60px;
  text-decoration: none;
  font-weight: 700;
  font-size: 1.1rem;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-width: 200px;
  justify-content: center;
}

.cta-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.cta-button:hover::before {
  left: 100%;
}

.cta-button.primary {
  background: linear-gradient(135deg, var(--highlight-color) 0%, rgba(255, 255, 255, 0.9) 50%, var(--highlight-color) 100%);
  color: var(--primary-color);
  box-shadow:
    0 15px 35px rgba(255, 255, 255, 0.25),
    0 5px 15px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.cta-button.primary:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow:
    0 20px 50px rgba(255, 255, 255, 0.4),
    0 10px 25px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  background: linear-gradient(135deg, var(--highlight-color) 0%, rgba(255, 255, 255, 1) 50%, var(--highlight-color) 100%);
}

.cta-button.secondary {
  background: rgba(255, 255, 255, 0.05);
  color: var(--highlight-color);
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.cta-button.secondary:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: var(--highlight-color);
  color: var(--highlight-color);
  transform: translateY(-4px) scale(1.05);
  box-shadow:
    0 15px 40px rgba(255, 255, 255, 0.2),
    0 5px 15px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.cta-button.large {
  padding: 20px 40px;
  font-size: 1.2rem;
  min-width: 220px;
}

.cta-button i {
  transition: transform 0.3s ease;
}

.cta-button:hover i {
  transform: translateX(3px);
}

.cta-button.secondary:hover i {
  transform: translateY(-2px);
}

/* Hero Particles */
.hero-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.particle {
  position: absolute;
  border-radius: 50%;
  animation: particleFloat 6s ease-in-out infinite;
  opacity: 0.6;
}

.particle:nth-child(1) {
  width: 6px;
  height: 6px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.2));
  top: 15%;
  left: 15%;
  animation-delay: 0s;
  animation-duration: 8s;
}

.particle:nth-child(2) {
  width: 4px;
  height: 4px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.1));
  top: 25%;
  left: 85%;
  animation-delay: 2s;
  animation-duration: 6s;
}

.particle:nth-child(3) {
  width: 8px;
  height: 8px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.2));
  top: 75%;
  left: 35%;
  animation-delay: 4s;
  animation-duration: 10s;
}

.particle:nth-child(4) {
  width: 5px;
  height: 5px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.1));
  top: 35%;
  left: 75%;
  animation-delay: 1s;
  animation-duration: 7s;
}

.particle:nth-child(5) {
  width: 7px;
  height: 7px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.3));
  top: 65%;
  left: 5%;
  animation-delay: 3s;
  animation-duration: 9s;
}

.particle:nth-child(6) {
  width: 3px;
  height: 3px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.1));
  top: 45%;
  left: 25%;
  animation-delay: 5s;
  animation-duration: 11s;
}

.particle:nth-child(7) {
  width: 6px;
  height: 6px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.2));
  top: 85%;
  left: 65%;
  animation-delay: 1.5s;
  animation-duration: 8.5s;
}

.particle:nth-child(8) {
  width: 4px;
  height: 4px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.1));
  top: 10%;
  left: 55%;
  animation-delay: 3.5s;
  animation-duration: 7.5s;
}

.particle:nth-child(9) {
  width: 5px;
  height: 5px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.2));
  top: 55%;
  left: 90%;
  animation-delay: 2.5s;
  animation-duration: 9.5s;
}

.particle:nth-child(10) {
  width: 8px;
  height: 8px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.2));
  top: 90%;
  left: 15%;
  animation-delay: 4.5s;
  animation-duration: 6.5s;
}

.particle:nth-child(11) {
  width: 3px;
  height: 3px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.1));
  top: 20%;
  left: 45%;
  animation-delay: 6s;
  animation-duration: 8s;
}

.particle:nth-child(12) {
  width: 7px;
  height: 7px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.3));
  top: 40%;
  left: 10%;
  animation-delay: 0.5s;
  animation-duration: 10.5s;
}

.particle:nth-child(13) {
  width: 4px;
  height: 4px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.1));
  top: 12%;
  left: 70%;
  animation-delay: 7s;
  animation-duration: 9s;
}

.particle:nth-child(14) {
  width: 6px;
  height: 6px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.2));
  top: 78%;
  left: 85%;
  animation-delay: 2.8s;
  animation-duration: 11.5s;
}

.particle:nth-child(15) {
  width: 3px;
  height: 3px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.1));
  top: 50%;
  left: 5%;
  animation-delay: 4.2s;
  animation-duration: 7.8s;
}

.particle:nth-child(16) {
  width: 8px;
  height: 8px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.2));
  top: 30%;
  left: 95%;
  animation-delay: 1.8s;
  animation-duration: 12s;
}

.particle:nth-child(17) {
  width: 5px;
  height: 5px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.2));
  top: 95%;
  left: 50%;
  animation-delay: 5.5s;
  animation-duration: 8.2s;
}

.particle:nth-child(18) {
  width: 4px;
  height: 4px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.1));
  top: 8%;
  left: 30%;
  animation-delay: 3.2s;
  animation-duration: 9.8s;
}

.particle:nth-child(19) {
  width: 7px;
  height: 7px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.3));
  top: 62%;
  left: 78%;
  animation-delay: 6.5s;
  animation-duration: 10.2s;
}

.particle:nth-child(20) {
  width: 6px;
  height: 6px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.2));
  top: 18%;
  left: 8%;
  animation-delay: 4.8s;
  animation-duration: 11.8s;
}

/* Hero Stats */
.hero-stats {
  display: flex;
  justify-content: center;
  gap: 40px;
  margin: 40px 0;
  animation: fadeInUp 1.2s ease-out 1.5s both;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  min-width: 120px;
}

.stat-item:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 10px 30px rgba(255, 255, 255, 0.1);
}

.stat-number {
  font-size: 2rem;
  font-weight: 800;
  color: var(--highlight-color);
  margin-bottom: 5px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-label {
  font-size: 0.9rem;
  color: var(--text-color);
  opacity: 0.8;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.animate-stat {
  animation: scaleIn 0.8s ease-out both;
}

/* Enhanced hover effects */

.stat-item {
  position: relative;
  overflow: hidden;
}

.stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.6s ease;
}

.stat-item:hover::before {
  left: 100%;
}

/* Features Section */
.features-section {
  padding: 80px 20px;
  background: var(--secondary-color);
  background-image: var(--card-gradient);
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--highlight-color);
  margin-bottom: 15px;
}

.section-subtitle {
  font-size: 1.2rem;
  color: var(--text-color);
  opacity: 0.8;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.feature-card {
  background: var(--primary-color);
  background-image: var(--card-gradient);
  border-radius: 20px;
  padding: 30px;
  border: 1px solid var(--border-color);
  box-shadow: var(--card-shadow);
  transition: all var(--transition-speed) ease;
  position: relative;
  overflow: hidden;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.6);
}

.feature-card.live {
  border-color: var(--highlight-color);
  box-shadow: 0 10px 30px rgba(255, 255, 255, 0.1);
}

.feature-card.live:hover {
  box-shadow: 0 20px 50px rgba(255, 255, 255, 0.2);
}

.feature-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(45deg, var(--accent-color), var(--accent-light));
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 25px;
  font-size: 2rem;
  color: var(--highlight-color);
}

.feature-card.live .feature-icon {
  background: linear-gradient(45deg, var(--highlight-color), var(--accent-light));
  color: var(--primary-color);
}

.feature-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--highlight-color);
  margin-bottom: 15px;
}

.feature-description {
  color: var(--text-color);
  line-height: 1.6;
  margin-bottom: 20px;
}

.feature-highlights {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 25px;
}

.highlight-tag {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  background: var(--accent-color);
  color: var(--text-color);
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
}

.feature-card.live .highlight-tag {
  background: rgba(255, 255, 255, 0.1);
  color: var(--highlight-color);
}

.feature-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 25px;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  transition: all var(--transition-speed) ease;
  border: none;
  cursor: pointer;
  font-size: 0.95rem;
}

.live-button {
  background: linear-gradient(45deg, var(--highlight-color), var(--accent-light));
  color: var(--primary-color);
}

.live-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(255, 255, 255, 0.3);
}

.upcoming-button {
  background: var(--accent-color);
  color: var(--text-color);
  cursor: not-allowed;
  opacity: 0.7;
}

.live-badge, .upcoming-badge {
  position: absolute;
  top: 20px;
  right: 20px;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 5px;
}

.live-badge {
  background: linear-gradient(45deg, #00ff00, #32cd32);
  color: var(--primary-color);
}

.live-badge i {
  animation: pulse 2s infinite;
}

.upcoming-badge {
  background: var(--accent-color);
  color: var(--text-color);
}

/* Why Choose Section */
.why-choose-section {
  padding: 80px 20px;
  background: var(--primary-color);
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.benefit-card {
  background: var(--secondary-color);
  background-image: var(--card-gradient);
  border-radius: 15px;
  padding: 30px;
  text-align: center;
  border: 1px solid var(--border-color);
  transition: all var(--transition-speed) ease;
}

.benefit-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--card-shadow);
}

.benefit-icon {
  width: 70px;
  height: 70px;
  background: linear-gradient(45deg, var(--accent-color), var(--accent-light));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  font-size: 1.8rem;
  color: var(--highlight-color);
}

.benefit-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--highlight-color);
  margin-bottom: 15px;
}

.benefit-description {
  color: var(--text-color);
  line-height: 1.6;
}

/* CTA Section */
.cta-section {
  padding: 80px 20px;
  background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
  text-align: center;
}

.cta-content {
  max-width: 600px;
  margin: 0 auto;
}

.cta-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--highlight-color);
  margin-bottom: 20px;
}

.cta-description {
  font-size: 1.2rem;
  color: var(--text-color);
  margin-bottom: 40px;
  opacity: 0.9;
}

.cta-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.1);
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  25% {
    background-position: 100% 50%;
  }
  50% {
    background-position: 200% 50%;
  }
  75% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    background-position: 200% 0;
    opacity: 0;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
  }
  50% {
    box-shadow: 0 0 40px rgba(255, 255, 255, 0.3);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes logoFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
}

@keyframes logoRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes logoGlow {
  0% {
    text-shadow: 0 2px 10px rgba(255, 255, 255, 0.3);
  }
  100% {
    text-shadow: 0 2px 20px rgba(255, 255, 255, 0.6), 0 0 30px rgba(255, 255, 255, 0.2);
  }
}

@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.6;
  }
  25% {
    transform: translateY(-20px) translateX(10px);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-15px) translateX(-5px);
    opacity: 1;
  }
  75% {
    transform: translateY(-25px) translateX(15px);
    opacity: 0.7;
  }
}

@keyframes advancedGradientFlow {
  0% {
    background-position: 0% 50%;
    transform: scale(1) rotateX(0deg);
  }
  15% {
    background-position: 25% 25%;
    transform: scale(1.02) rotateX(2deg);
  }
  30% {
    background-position: 50% 0%;
    transform: scale(1.04) rotateX(0deg);
  }
  45% {
    background-position: 75% 25%;
    transform: scale(1.02) rotateX(-2deg);
  }
  60% {
    background-position: 100% 50%;
    transform: scale(1) rotateX(0deg);
  }
  75% {
    background-position: 75% 75%;
    transform: scale(1.02) rotateX(2deg);
  }
  90% {
    background-position: 25% 100%;
    transform: scale(1.04) rotateX(0deg);
  }
  100% {
    background-position: 0% 50%;
    transform: scale(1) rotateX(0deg);
  }
}

@keyframes modernShimmer {
  0% {
    background-position: -300% 0;
    opacity: 0;
    transform: skewX(-15deg);
  }
  30% {
    opacity: 0.3;
  }
  50% {
    background-position: 0% 0;
    opacity: 1;
    transform: skewX(0deg);
  }
  70% {
    opacity: 0.3;
  }
  100% {
    background-position: 300% 0;
    opacity: 0;
    transform: skewX(15deg);
  }
}

@keyframes lightSweep {
  0% {
    background-position: -200% 0;
    opacity: 0;
  }
  20% {
    opacity: 0.5;
  }
  50% {
    background-position: 0% 0;
    opacity: 1;
  }
  80% {
    opacity: 0.5;
  }
  100% {
    background-position: 200% 0;
    opacity: 0;
  }
}

/* Blog Section */
.blog-section {
  padding: 80px 20px;
  background: var(--secondary-color);
  background-image: var(--card-gradient);
}

.blog-preview-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  max-width: 1200px;
  margin: 0 auto;
  margin-bottom: 50px;
}

.blog-featured-card {
  background: var(--primary-color);
  background-image: var(--card-gradient);
  border-radius: 20px;
  overflow: hidden;
  border: 1px solid var(--border-color);
  box-shadow: var(--card-shadow);
  transition: all var(--transition-speed) ease;
}

.blog-featured-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.6);
}

.blog-image {
  position: relative;
  overflow: hidden;
}

.blog-image img {
  width: 100%;
  height: 300px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.blog-featured-card:hover .blog-image img {
  transform: scale(1.05);
}

.blog-category-badge {
  position: absolute;
  top: 15px;
  left: 15px;
  background: linear-gradient(45deg, var(--highlight-color), var(--accent-light));
  color: var(--primary-color);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.blog-content {
  padding: 30px;
}

.blog-meta {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 15px;
  font-size: 0.9rem;
  color: var(--text-color);
  opacity: 0.8;
}

.blog-category {
  background: var(--accent-color);
  color: var(--highlight-color);
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.blog-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--highlight-color);
  margin-bottom: 15px;
  line-height: 1.3;
}

.blog-excerpt {
  color: var(--text-color);
  line-height: 1.6;
  margin-bottom: 25px;
  opacity: 0.9;
}

.blog-read-more {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  color: var(--highlight-color);
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.blog-read-more:hover {
  color: var(--accent-light);
  transform: translateX(5px);
}

.blog-recent-posts {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.blog-card {
  background: var(--primary-color);
  background-image: var(--card-gradient);
  border-radius: 15px;
  overflow: hidden;
  border: 1px solid var(--border-color);
  transition: all var(--transition-speed) ease;
  display: flex;
  gap: 20px;
}

.blog-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4);
}

.blog-card-image {
  flex-shrink: 0;
  width: 120px;
  height: 120px;
  overflow: hidden;
}

.blog-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.blog-card:hover .blog-card-image img {
  transform: scale(1.1);
}

.blog-card-content {
  padding: 20px 20px 20px 0;
  flex: 1;
}

.blog-card-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--highlight-color);
  margin-bottom: 10px;
  line-height: 1.3;
}

.blog-card-excerpt {
  color: var(--text-color);
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 15px;
  opacity: 0.8;
}

.blog-card-link {
  color: var(--highlight-color);
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 600;
  transition: color 0.3s ease;
}

.blog-card-link:hover {
  color: var(--accent-light);
}

.blog-cta {
  text-align: center;
}

.blog-view-all-btn {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  padding: 18px 35px;
  background: linear-gradient(135deg, var(--highlight-color), var(--accent-light));
  color: var(--primary-color);
  text-decoration: none;
  border-radius: 50px;
  font-weight: 700;
  font-size: 1.1rem;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 10px 25px rgba(255, 255, 255, 0.2);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.blog-view-all-btn:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 15px 35px rgba(255, 255, 255, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-landing {
    padding: 80px 15px 50px;
  }

  .hero-title {
    font-size: 2.8rem;
    line-height: 1.3;
    flex-direction: column;
    gap: 0.1em;
    text-align: center;
  }

  .welcome-text {
    font-size: 0.9em;
  }

  .brand-highlight {
    font-size: 1.1em;
  }

  .hero-subtitle {
    font-size: 1.4rem;
  }

  .hero-description {
    font-size: 1.1rem;
    margin-bottom: 35px;
  }

  .hero-stats {
    gap: 20px;
    margin: 30px 0;
  }

  .stat-item {
    min-width: 100px;
    padding: 15px;
  }

  .stat-number {
    font-size: 1.6rem;
  }

  .stat-label {
    font-size: 0.8rem;
  }

  .hero-cta {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }

  .cta-button {
    width: 100%;
    max-width: 280px;
    justify-content: center;
    padding: 16px 30px;
    font-size: 1rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .benefits-grid {
    grid-template-columns: 1fr;
  }

  .section-title {
    font-size: 2rem;
  }

  .cta-title {
    font-size: 2rem;
  }

  .blog-preview-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .blog-card {
    flex-direction: column;
  }

  .blog-card-image {
    width: 100%;
    height: 200px;
  }

  .blog-card-content {
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .hero-landing {
    padding: 70px 15px 40px;
  }

  .hero-title {
    font-size: 2.2rem;
    margin-bottom: 20px;
    flex-direction: column;
    gap: 0.05em;
    line-height: 1.2;
  }

  .welcome-text {
    font-size: 0.85em;
    margin-bottom: 0.1em;
  }

  .brand-highlight {
    font-size: 1.15em;
  }

  .hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 20px;
  }

  .hero-description {
    font-size: 1rem;
    margin-bottom: 30px;
  }

  .logo-circle {
    width: 100px;
    height: 100px;
  }

  .logo-inner {
    width: 70px;
    height: 70px;
  }

  .favicon-logo {
    width: 40px;
    height: 40px;
  }

  .logo-ring-2 {
    width: 120px;
    height: 120px;
    top: -10px;
    left: -10px;
  }

  .hero-stats {
    flex-direction: column;
    gap: 15px;
    margin: 25px 0;
  }

  .stat-item {
    min-width: auto;
    width: 100%;
    max-width: 200px;
    margin: 0 auto;
  }

  .cta-button {
    padding: 14px 25px;
    font-size: 0.95rem;
    min-width: auto;
  }

  .particle {
    display: none;
  }

  .feature-card, .benefit-card {
    padding: 20px;
  }

  .features-section, .why-choose-section, .cta-section, .blog-section {
    padding: 60px 15px;
  }

  .blog-title {
    font-size: 1.5rem;
  }

  .blog-card-image {
    height: 180px;
  }
}

/* Extra small screens */
@media (max-width: 360px) {
  .hero-title {
    font-size: 1.9rem;
    margin-bottom: 15px;
  }

  .welcome-text {
    font-size: 0.8em;
  }

  .brand-highlight {
    font-size: 1.2em;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .hero-description {
    font-size: 0.95rem;
  }

  .logo-circle {
    width: 80px;
    height: 80px;
  }

  .logo-inner {
    width: 60px;
    height: 60px;
  }

  .favicon-logo {
    width: 35px;
    height: 35px;
  }

  .logo-ring-2 {
    width: 100px;
    height: 100px;
    top: -10px;
    left: -10px;
  }


}
