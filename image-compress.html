<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Image Compressor - ImgNinja</title>
  <link rel="stylesheet" href="css/styles.css">
  <link rel="stylesheet" href="css/responsive.css">
  <link rel="stylesheet" href="css/crop.css">
  <!-- Add Poppins Font -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
  <!-- Add Font Awesome Icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
  <!-- Favicon -->
  <link rel="icon" href="assets/favicon-imgNinja.png" type="image/png">
</head>
<body>
  <!-- Navigation Bar -->
  <nav>
    <div class="logo">
      <a href="index.html">
        <img src="assets/logo-imgNinja.png" alt="ImgNinja Logo" width="170" height="90">
      </a>
    </div>
    <div class="menu-toggle" id="mobile-menu">
      <span class="bar"></span>
      <span class="bar"></span>
      <span class="bar"></span>
    </div>
    <ul class="nav-links">
      <li><a href="index.html"><i class="fas fa-home"></i> Home</a></li>
      <li><a href="pages/about-us.html"><i class="fas fa-info-circle"></i> About</a></li>
      <li><a href="blog/index.html"><i class="fas fa-blog"></i> Blog</a></li>
      <li><a href="image-compress.html"><i class="fas fa-image"></i> Image Compressor</a></li>
    </ul>
  </nav>

  <!-- Main Content -->
  <div class="container">
    <div class="hero-section">
      <div class="hero-content">
       <h1>
          <span class="pdf-title">Image Compressor</span>
      </h1>
        <p class="subtitle">Compress JPG, PNG, JPEG, WEBP, GIF, HEIF/HEIC, ICO, and SVG without losing quality.</p>
        <p class="hero-description">Advanced compression technology that preserves image quality while reducing file size by up to 90%.</p>
      </div>
    </div>
    <div class="upload-box" id="drop-area">
      <div class="upload-icon">
        <i class="fas fa-cloud-upload-alt"></i>
      </div>
      <input type="file" id="upload" accept="image/jpeg, image/png, image/jpg, image/webp, image/gif, image/heif, image/heic, image/x-icon, image/svg+xml, .heif, .heic, .ico, .svg">
      <label for="upload" class="upload-label"><i class="fas fa-cloud-upload-alt"></i> Choose an Image</label>
      <p class="drag-text">or drag and drop your image here</p>
      <div class="upload-info">
        <p class="formats"><i class="fas fa-file-image"></i> Supported Formats: JPG, JPEG, PNG, WEBP, GIF, HEIF/HEIC, ICO, SVG</p>
        <p class="max-size"><i class="fas fa-weight-hanging"></i> Max File Size: 10MB</p>
      </div>
      <div id="image-preview"></div>
      <!-- Image Details Integrated Here -->
      <div id="image-details" class="details-card">
        <div class="details-header">
          <i class="fas fa-info-circle"></i> Image Details
        </div>
        <div class="details-content">
          <p id="dimensions-px"><i class="fas fa-ruler-combined"></i> Dimensions: <span></span></p>
          <p id="dimensions-mm"><i class="fas fa-ruler"></i> Dimensions: <span></span></p>
          <p id="dimensions-cm"><i class="fas fa-ruler"></i> Dimensions: <span></span></p>
          <p id="size-display"><i class="fas fa-database"></i> Size: <span></span></p>
        </div>
      </div>
    </div>

    <!-- Manual Compression Settings -->
    <div class="settings-card">
      <div class="settings-header">
        <h3><i class="fas fa-sliders-h"></i> Compression Settings</h3>
        <div class="settings-tabs">
          <button class="settings-tab active tooltip" data-tab="quality">
            <i class="fas fa-compress"></i> Quality
            <span class="tooltip-text">Adjust compression quality</span>
          </button>
          <button class="settings-tab tooltip" data-tab="dimensions">
            <i class="fas fa-arrows-alt"></i> Dimensions
            <span class="tooltip-text">Resize image dimensions</span>
          </button>
          <button class="settings-tab tooltip" data-tab="size">
            <i class="fas fa-file-archive"></i> Target Size
            <span class="tooltip-text">Set specific file size</span>
          </button>
        </div>
      </div>

      <div class="settings">
        <div class="settings-panel active" id="quality-panel">
          <div class="compression-level-container">
            <div class="setting-label">
              <label for="compression-level"><i class="fas fa-compress"></i> Compression Level:</label>
            </div>
            <div class="slider-container">
              <div class="slider-track">
                <div class="slider-fill"></div>
                <input type="range" id="compression-level" min="0" max="100" value="70">
                <div class="slider-thumb"></div>
              </div>
              <div class="compression-value-container">
                <span id="compression-value">70%</span>
              </div>
            </div>
          </div>
        </div>

        <div class="settings-panel" id="dimensions-panel">
          <div class="dimension-inputs">
            <div class="setting">
              <label for="width"><i class="fas fa-arrows-alt-h"></i> Width (px):</label>
              <input type="number" id="width" placeholder="Auto" class="dimension-input">
            </div>
            <div class="aspect-ratio-link">
              <i class="fas fa-link" title="Aspect ratio will be maintained"></i>
            </div>
            <div class="setting">
              <label for="height"><i class="fas fa-arrows-alt-v"></i> Height (px):</label>
              <input type="number" id="height" placeholder="Auto" class="dimension-input">
            </div>
          </div>
        </div>

        <div class="settings-panel" id="size-panel">
          <div class="target-size-container">
            <div class="setting">
              <label for="target-size"><i class="fas fa-file-archive"></i> Target Size (KB):</label>
              <input type="number" id="target-size" placeholder="Enter size in KB" class="dimension-input" min="1">
            </div>
          </div>
        </div>
      </div>
    </div>

    <button id="compress-btn" class="action-button"><i class="fas fa-compress-arrows-alt"></i> Compress Image</button>

    <div class="result-container">
      <div id="preview" class="preview-container"></div>
      <div class="result-info">
        <p id="compressed-size-display"><i class="fas fa-file-archive"></i> <span></span></p>
        <div class="action-buttons">
          <a id="download-link" class="action-button" style="display: none;"><i class="fas fa-download"></i> Download Compressed Image</a>
          <button id="new-image-btn" class="action-button secondary" style="display: none;"><i class="fas fa-redo-alt"></i> New Image</button>
        </div>
      </div>
    </div>

    <!-- How It Works Section -->
    <div class="how-it-works">
      <h2><i class="fas fa-cogs"></i> How It Works</h2>
      <div class="steps-container">
        <div class="step">
          <div class="step-icon">
            <i class="fas fa-upload"></i>
            <div class="step-number">1</div>
          </div>
          <h3>Upload Your Image</h3>
          <p>Select an image from your device or drag and drop it into the upload area. We support JPG, PNG, JPEG, WEBP, GIF, HEIF/HEIC, ICO, and SVG formats.</p>
        </div>
        <div class="step">
          <div class="step-icon">
            <i class="fas fa-sliders-h"></i>
            <div class="step-number">2</div>
          </div>
          <h3>Adjust Settings</h3>
          <p>Choose your desired compression level using the slider, specify dimensions (aspect ratio is automatically maintained), or set a target file size for your compressed image.</p>
        </div>
        <div class="step">
          <div class="step-icon">
            <i class="fas fa-compress-arrows-alt"></i>
            <div class="step-number">3</div>
          </div>
          <h3>Compress</h3>
          <p>Click the "Compress Image" button to start the compression process. Our advanced algorithms will optimize your image while preserving quality.</p>
        </div>
        <div class="step">
          <div class="step-icon">
            <i class="fas fa-download"></i>
            <div class="step-number">4</div>
          </div>
          <h3>Download</h3>
          <p>Once compression is complete, preview the result and download your optimized image. It's that simple!</p>
        </div>
      </div>
      <div class="tech-info">
        <h3><i class="fas fa-microchip"></i> Our Technology</h3>
        <p>ImgNinja uses advanced compression algorithms that intelligently analyze your images to reduce file size while maintaining visual quality. Our technology works entirely in your browser - your images are never uploaded to any server, ensuring complete privacy and security.</p>
        <div class="blog-promo">
          <p><i class="fas fa-lightbulb"></i> Want to learn more about image compression? Check out our <a href="blog/index.html">blog</a> for tips, tutorials, and best practices.</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer -->
  <footer>
    <div class="footer-content">
      <!-- Column 1: Logo and About -->
      <div class="footer-column">
        <a href="#" class="footer-logo">
          <img src="assets/logo-imgNinja.png" alt="ImgNinja Logo" width="200" height="110">
        </a>
        <p class="footer-description">
          ImgNinja provides powerful, free online tools for image compression, helping you optimize your images without sacrificing quality.
        </p>

      </div>

      <!-- Column 2: Quick Links -->
      <div class="footer-column">
        <h3 class="footer-heading">Quick Links</h3>
        <div class="footer-links">
          <a href="index.html"><i class="fas fa-home"></i> Home</a>
          <a href="blog/index.html"><i class="fas fa-blog"></i> Blog</a>
          <a href="pages/about-us.html"><i class="fas fa-info-circle"></i> About Us</a>
          <a href="pages/contact-us.html"><i class="fas fa-envelope"></i> Contact Us</a>
        </div>
      </div>

      <!-- Column 3: Legal -->
      <div class="footer-column">
        <h3 class="footer-heading">Legal</h3>
        <div class="footer-links">
          <a href="pages/privacy-policy.html"><i class="fas fa-shield-alt"></i> Privacy Policy</a>
          <a href="pages/terms-conditions.html"><i class="fas fa-file-contract"></i> Terms & Conditions</a>
          <a href="pages/dmca.html"><i class="fas fa-copyright"></i> DMCA</a>
          <a href="pages/privacy-policy.html"><i class="fas fa-cookie"></i> Cookie Policy</a>
        </div>
      </div>

      <!-- Column 4: Tools -->
      <div class="footer-column">
        <h3 class="footer-heading">Our Tools</h3>
        <div class="footer-links">
          <a href="image-compress.html"><i class="fas fa-image"></i> Image Compressor</a>
          <a href="#"><i class="fas fa-crop-alt"></i> Image Resizer</a>
        </div>
      </div>

      <!-- Footer Bottom -->
      <div class="footer-bottom">
        <p class="copyright">&copy; 2025 ImgNinja. All rights reserved. | Made by <a href="https://totalsolution.42web.io/" target="_blank">Total Solution</a></p>
      </div>
    </div>

    <!-- Back to Top Button -->
    <a href="#" class="back-to-top" title="Back to Top"><i class="fas fa-arrow-up"></i></a>
  </footer>

  <!-- Back to Top Script -->
  <script>
    // Back to top button functionality
    document.addEventListener('DOMContentLoaded', function() {
      const backToTopButton = document.querySelector('.back-to-top');

      // Show/hide button based on scroll position
      window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
          backToTopButton.classList.add('visible');
        } else {
          backToTopButton.classList.remove('visible');
        }
      });

      // Smooth scroll to top when clicked
      backToTopButton.addEventListener('click', function(e) {
        e.preventDefault();
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      });
    });
  </script>

  <!-- Add Cropper.js library -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.13/cropper.min.js"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.13/cropper.min.css">

  <!-- Add libheif.js for HEIF/HEIC support -->
  <script src="https://unpkg.com/libheif-js@1.10.0/dist/libheif.js"></script>

  <!-- App Scripts -->
  <script src="js/script.js"></script>
  <script src="js/crop.js"></script>
</body>
</html>